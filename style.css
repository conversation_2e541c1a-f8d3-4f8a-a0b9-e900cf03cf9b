@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Neonderthaw&display=swap');

:root {
  /* === ENHANCED MONOCHROMATIC PURPLE PALETTE === */
  --primary: #7C2D92;          /* Deep Purple - Main brand color */
  --secondary: #9333EA;        /* Medium Purple - Interactive elements */
  --accent: #A855F7;           /* Bright Purple - Highlights & accents */
  --dark-base: #0D0615;        /* Ultra Dark Purple - Primary background */
  --dark-secondary: #1A0B2E;   /* Very Dark Purple - Secondary background */
  --dark-tertiary: #2D1B4E;    /* Dark Purple - Card backgrounds */

  /* === ENHANCED GRADIENT BACKGROUNDS === */
  --gradient-main: linear-gradient(135deg, #0D0615 0%, #1A0B2E 25%, #2D1B4E 60%, #44246A 85%, #5B2C87 100%);
  --gradient-dark: radial-gradient(ellipse at center, #1A0B2E 0%, #0D0615 70%);
  --gradient-ultra-dark: linear-gradient(135deg, #0D0615 0%, #130920 50%, #0D0615 100%);
  --gradient-card: linear-gradient(135deg, rgba(124, 45, 146, 0.12) 0%, rgba(147, 51, 234, 0.06) 100%);
  --gradient-card-hover: linear-gradient(135deg, rgba(124, 45, 146, 0.20) 0%, rgba(147, 51, 234, 0.12) 100%);
  --gradient-subtle: linear-gradient(135deg, rgba(45, 27, 78, 0.3) 0%, rgba(26, 11, 46, 0.1) 100%);

  /* === ENHANCED GLASSMORPHISM EFFECTS === */
  --glass-bg: rgba(147, 51, 234, 0.08);
  --glass-bg-strong: rgba(147, 51, 234, 0.15);
  --glass-border: rgba(168, 85, 247, 0.2);
  --glass-border-strong: rgba(168, 85, 247, 0.35);
  --glass-shadow: 0 8px 32px rgba(13, 6, 21, 0.8);
  --glass-shadow-strong: 0 12px 48px rgba(13, 6, 21, 0.9);
  --glass-blur: blur(24px);
  --glass-blur-strong: blur(32px);

  /* === ENHANCED TEXT & OUTLINE === */
  --text-primary: #FFFFFF;
  --text-secondary: #E8E0F5;    /* Lighter purple-tinted white */
  --text-tertiary: #D1C7E0;     /* Medium purple-tinted white */
  --text-muted: #B794C7;        /* Muted purple */
  --text-dimmed: #9B7BA8;       /* Dimmed purple */
  --outline-stroke: #A855F7;
  --outline-glow: 0 0 25px rgba(168, 85, 247, 0.5);
  --outline-glow-strong: 0 0 40px rgba(168, 85, 247, 0.7);

  /* === ENHANCED INTERACTIVE STATES === */
  --hover-primary: #8B5CF6;
  --hover-secondary: #A855F7;
  --hover-accent: #C084FC;
  --focus-ring: rgba(168, 85, 247, 0.6);
  --focus-ring-strong: rgba(168, 85, 247, 0.8);

  /* === SEMANTIC COLORS === */
  --success: #22C55E;
  --success-dark: #16A34A;
  --warning: #F59E0B;
  --warning-dark: #D97706;
  --error: #EF4444;
  --error-dark: #DC2626;
  --info: var(--accent);

  /* === SPACING & SIZING === */
  --border-radius: 16px;
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 24px;
  --border-radius-xl: 32px;

  /* === ENHANCED SHADOWS === */
  --shadow-xs: 0 1px 4px rgba(13, 6, 21, 0.4);
  --shadow-sm: 0 2px 8px rgba(13, 6, 21, 0.5);
  --shadow-md: 0 4px 16px rgba(13, 6, 21, 0.6);
  --shadow-lg: 0 8px 32px rgba(13, 6, 21, 0.7);
  --shadow-xl: 0 12px 48px rgba(13, 6, 21, 0.8);
  --shadow-glow: 0 0 40px rgba(124, 45, 146, 0.4);
  --shadow-glow-strong: 0 0 60px rgba(124, 45, 146, 0.6);
  --shadow-purple: 0 8px 32px rgba(147, 51, 234, 0.3);

  /* === ADDITIONAL PURPLE SHADES FOR VARIETY === */
  --purple-50: #F3E8FF;        /* Very light purple for rare light elements */
  --purple-100: #E9D5FF;       /* Light purple */
  --purple-200: #DDD6FE;       /* Lighter purple */
  --purple-300: #C4B5FD;       /* Medium-light purple */
  --purple-400: #A78BFA;       /* Medium purple */
  --purple-500: #8B5CF6;       /* Standard purple */
  --purple-600: #7C3AED;       /* Darker purple */
  --purple-700: #6D28D9;       /* Deep purple */
  --purple-800: #5B21B6;       /* Very deep purple */
  --purple-900: #4C1D95;       /* Darkest purple */
  --purple-950: #2E1065;       /* Ultra dark purple */

  /* === ANIMATION VARIABLES === */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* === ENHANCED UTILITY CLASSES === */

/* Main backgrounds with darker tones */
.bg-main {
  background: var(--gradient-main);
  min-height: 100vh;
}

.bg-dark {
  background: var(--gradient-dark);
  min-height: 100vh;
}

.bg-ultra-dark {
  background: var(--gradient-ultra-dark);
  min-height: 100vh;
}

.bg-solid-dark {
  background: var(--dark-base);
  min-height: 100vh;
}

/* Enhanced glassmorphism cards */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
}

.glass-card:hover {
  background: var(--gradient-card-hover);
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
  border-color: var(--glass-border-strong);
}

.glass-card-strong {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur-strong);
  border: 1px solid var(--glass-border-strong);
  border-radius: var(--border-radius);
  box-shadow: var(--glass-shadow-strong);
  transition: all var(--transition-normal);
}

.glass-card-strong:hover {
  background: var(--gradient-card-hover);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-xl), var(--shadow-glow-strong);
}

/* Subtle card variant with darker tones */
.glass-card-subtle {
  background: rgba(45, 27, 78, 0.06);
  backdrop-filter: var(--glass-blur);
  border: 1px solid rgba(168, 85, 247, 0.12);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 16px rgba(13, 6, 21, 0.5);
  transition: all var(--transition-normal);
}

.glass-card-subtle:hover {
  background: rgba(45, 27, 78, 0.12);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Enhanced outline text styles */
.text-outline {
  -webkit-text-stroke: 1px var(--outline-stroke);
  -webkit-text-fill-color: transparent;
  font-weight: 600;
  text-shadow: 0 0 20px rgba(168, 85, 247, 0.2),
               0 0 40px rgba(168, 85, 247, 0.1),
               0 0 60px rgba(168, 85, 247, 0.05);
  transition: all var(--transition-normal);
}

.text-outline-filled {
  -webkit-text-stroke: 1px var(--outline-stroke);
  -webkit-text-fill-color: var(--text-primary);
  font-weight: 600;
  text-shadow: var(--outline-glow);
}

/* Enhanced button styles */
.btn-primary {
  background: var(--gradient-card);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  padding: 12px 24px;
  border-radius: var(--border-radius-sm);
  backdrop-filter: var(--glass-blur);
  transition: all var(--transition-normal);
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary:hover {
  background: var(--hover-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md), var(--shadow-purple);
  border-color: var(--glass-border-strong);
}

.btn-primary:active {
  transform: translateY(0px);
  transition: all var(--transition-fast);
}

.btn-primary:focus {
  outline: 2px solid var(--focus-ring);
  outline-offset: 2px;
}

/* Enhanced accent button */
.btn-accent {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border: none;
  color: var(--text-primary);
  padding: 14px 28px;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-normal);
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(124, 45, 146, 0.4);
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.btn-accent::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left var(--transition-slow);
}

.btn-accent:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(124, 45, 146, 0.6);
}

.btn-accent:hover::before {
  left: 100%;
}

.btn-accent:active {
  transform: translateY(-1px);
  transition: all var(--transition-fast);
}

/* Ghost button variant */
.btn-ghost {
  background: transparent;
  border: 2px solid var(--glass-border);
  color: var(--text-primary);
  padding: 10px 22px;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-normal);
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-ghost:hover {
  background: var(--glass-bg);
  border-color: var(--accent);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Enhanced text colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }
.text-dimmed { color: var(--text-dimmed); }
.text-accent { color: var(--accent); }

/* Enhanced gradient text */
.text-gradient {
  background: linear-gradient(135deg, var(--secondary), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.text-gradient-alt {
  background: linear-gradient(135deg, var(--primary), var(--purple-400));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.text-gradient-rainbow {
  background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent), var(--hover-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
  font-weight: 700;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Enhanced section dividers */
.section-divider {
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent), transparent);
  border: none;
  margin: 3rem 0;
  border-radius: 1px;
}

.section-divider-thick {
  height: 4px;
  background: linear-gradient(90deg, transparent, var(--primary), var(--secondary), var(--accent), transparent);
  border: none;
  margin: 3rem 0;
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(168, 85, 247, 0.3);
}

/* Enhanced glow effects */
.glow-purple {
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
  transition: all var(--transition-normal);
}

.glow-purple:hover {
  box-shadow: 0 0 40px rgba(168, 85, 247, 0.6);
}

.glow-purple-strong {
  box-shadow: 0 0 30px rgba(168, 85, 247, 0.5);
  transition: all var(--transition-normal);
}

.glow-purple-strong:hover {
  box-shadow: 0 0 50px rgba(168, 85, 247, 0.8);
}

/* Animated elements */
.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
  }
  50% { 
    box-shadow: 0 0 40px rgba(168, 85, 247, 0.6);
  }
}

.float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Backdrop patterns for added visual interest */
.bg-pattern-dots {
  background-image: radial-gradient(circle, rgba(168, 85, 247, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-pattern-grid {
  background-image: 
    linear-gradient(rgba(168, 85, 247, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(168, 85, 247, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-base);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(var(--primary), var(--secondary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(var(--secondary), var(--accent));
}

/* Selection styling */
::selection {
  background: rgba(168, 85, 247, 0.3);
  color: var(--text-primary);
}

::-moz-selection {
  background: rgba(168, 85, 247, 0.3);
  color: var(--text-primary);
}
body{
  background: var(--gradient-main);
}

